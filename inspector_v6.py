import asyncio
import argparse
from crawl4ai import AsyncWebCrawler
from datetime import datetime
import json

from rich import print as rprint

async def main():
    rprint("[bold yellow]DIAGNOSTIC MODE SCRIPT[/bold yellow]")
    rprint("[italic]职责：捕获一个爬取结果对象，并打印其内部所有可用的属性。[/italic]\n")

    # --- 参数解析 (简化版，我们只需要URL) ---
    parser = argparse.ArgumentParser(description="诊断版脚本，用于检查CrawlResult对象的内部结构。", formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("--url", required=True, help="用于诊断的单个目标网页URL。")
    args = parser.parse_args()

    # --- 核心诊断逻辑 ---
    # 配置爬虫参数，增加超时时间和重试机制
    crawler_config = {
        'headless': True,
        'verbose': True,
        'page_timeout': 120000,  # 增加页面超时时间到120秒
        'request_timeout': 30000,  # 请求超时30秒
    }

    async with AsyncWebCrawler(**crawler_config) as crawler:
        rprint(f"正在尝试爬取诊断目标URL: {args.url}")

        # 尝试多种策略来处理超时问题
        strategies = [
            {"wait_for": "domcontentloaded", "timeout": 120000},  # 等待DOM加载完成
            {"wait_for": "networkidle", "timeout": 90000},        # 等待网络空闲
            {"wait_for": "load", "timeout": 60000},               # 等待完全加载
        ]

        result = None
        last_error = None

        for i, strategy in enumerate(strategies, 1):
            try:
                rprint(f"[yellow]尝试策略 {i}/{len(strategies)}: {strategy['wait_for']}[/yellow]")

                # 使用不同的等待策略
                result = await crawler.arun(
                    url=args.url,
                    wait_for=strategy['wait_for'],
                    timeout=strategy['timeout'],
                    page_timeout=strategy['timeout']
                )

                if result:
                    rprint(f"[green]✅ 策略 {i} 成功！[/green]")
                    break

            except Exception as e:
                last_error = e
                rprint(f"[red]❌ 策略 {i} 失败: {str(e)[:100]}...[/red]")
                if i < len(strategies):
                    rprint(f"[yellow]正在尝试下一个策略...[/yellow]")
                continue

        if result is None:
            rprint(f"\n[bold red]所有策略都失败了。最后一个错误: {last_error}[/bold red]")
            rprint("[yellow]正在尝试最后的备用方案...[/yellow]")

            # 最后的备用方案：使用最基本的设置
            try:
                result = await crawler.arun(
                    url=args.url,
                    wait_for="commit",  # 最基本的等待
                    timeout=30000,      # 较短的超时时间
                    page_timeout=30000
                )
                if result:
                    rprint("[green]✅ 备用方案成功！[/green]")
            except Exception as e:
                rprint(f"\n[bold red]备用方案也失败了: {e}[/bold red]")
                rprint("[yellow]将尝试生成错误报告...[/yellow]")

                # 生成错误报告JSON
                error_data = {
                    "timestamp": datetime.now().isoformat(),
                    "url": args.url,
                    "error_type": "crawling_failed",
                    "strategies_tried": strategies,
                    "final_error": str(last_error),
                    "backup_error": str(e)
                }

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                error_filename = f"error_report_{timestamp}.json"

                try:
                    with open(error_filename, 'w', encoding='utf-8') as f:
                        json.dump(error_data, f, ensure_ascii=False, indent=2)
                    rprint(f"[yellow]错误报告已保存到: {error_filename}[/yellow]")
                except Exception as save_error:
                    rprint(f"[red]无法保存错误报告: {save_error}[/red]")

                return

        # 如果成功获取到结果，继续诊断
        if result:
            try:
                # 【核心诊断代码】
                # 成功获取到 result 对象后，打印其内部结构然后退出
                rprint("\n\n[bold magenta]============== DIAGNOSTIC MODE: RESULT INSPECTION ===============[/bold magenta]")
                rprint("成功获取到 'result' 对象。正在检查其内部结构...")

                # 打印对象类型
                object_type = str(type(result))
                rprint(f"\n[cyan]对象类型 (Object Type):[/cyan] {object_type}")

                # 准备要保存到JSON的数据
                diagnostic_data = {
                    "timestamp": datetime.now().isoformat(),
                    "url": args.url,
                    "object_type": object_type,
                    "attributes": None,
                    "methods": None
                }

                # 使用 vars() 打印所有属性及其值，这是最清晰的方式
                rprint("\n[cyan]对象的属性和值 (Attributes and Values via vars()):[/cyan]")
                try:
                    # vars() 会以字典形式展示对象的属性，非常直观
                    result_vars = vars(result)
                    rprint(result_vars)

                    # 将属性转换为可序列化的格式
                    serializable_vars = {}
                    for key, value in result_vars.items():
                        try:
                            # 尝试JSON序列化测试
                            json.dumps(value)
                            serializable_vars[key] = value
                        except (TypeError, ValueError):
                            # 如果不能序列化，转换为字符串
                            serializable_vars[key] = str(value)

                    diagnostic_data["attributes"] = serializable_vars

                except TypeError:
                    # 如果 vars() 不适用，我们回退到使用 dir()
                    rprint("[yellow]vars() failed. Falling back to dir() to list all attributes and methods...[/yellow]")
                    methods_and_attrs = dir(result)
                    rprint(methods_and_attrs)
                    diagnostic_data["methods"] = methods_and_attrs

                # 生成JSON文件
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                json_filename = f"diagnostic_result_{timestamp}.json"

                try:
                    with open(json_filename, 'w', encoding='utf-8') as f:
                        json.dump(diagnostic_data, f, ensure_ascii=False, indent=2)
                    rprint(f"\n[bold green]✅ 诊断数据已保存到: {json_filename}[/bold green]")
                except Exception as e:
                    rprint(f"\n[bold red]❌ 保存JSON文件时出错: {e}[/bold red]")

                rprint("\n[bold magenta]===================================================================[/bold magenta]\n")

                rprint("[bold green]诊断完成。请复制以上'DIAGNOSTIC MODE'块内的所有内容并回复。程序已终止。[/bold green]")

            except Exception as e:
                rprint(f"\n[bold red]处理诊断结果时发生错误: {e}[/bold red]")


if __name__ == "__main__":
    asyncio.run(main())