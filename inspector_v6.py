import asyncio
import os
import argparse
import pandas as pd
from crawl4ai import AsyncWebCrawler
from datetime import datetime
import json

from rich import print as rprint
from tqdm import tqdm

async def main():
    rprint("[bold yellow]DIAGNOSTIC MODE SCRIPT[/bold yellow]")
    rprint("[italic]职责：捕获一个爬取结果对象，并打印其内部所有可用的属性。[/italic]\n")

    # --- 参数解析 (简化版，我们只需要URL) ---
    parser = argparse.ArgumentParser(description="诊断版脚本，用于检查CrawlResult对象的内部结构。", formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("--url", required=True, help="用于诊断的单个目标网页URL。")
    args = parser.parse_args()

    # --- 核心诊断逻辑 ---
    # 我们暂时硬编码 headless=True，并打开 verbose 日志来观察过程
    async with Async<PERSON>ebCrawler(headless=True, verbose=True) as crawler:
        rprint(f"正在尝试爬取诊断目标URL: {args.url}")
        try:
            # 只执行爬取，不截图以加快速度
            result = await crawler.arun(url=args.url)

            # 【核心诊断代码】
            # 成功获取到 result 对象后，打印其内部结构然后退出
            rprint("\n\n[bold magenta]============== DIAGNOSTIC MODE: RESULT INSPECTION ===============[/bold magenta]")
            rprint("成功获取到 'result' 对象。正在检查其内部结构...")
            
            # 打印对象类型
            rprint(f"\n[cyan]对象类型 (Object Type):[/cyan] {type(result)}")
            
            # 使用 vars() 打印所有属性及其值，这是最清晰的方式
            rprint("\n[cyan]对象的属性和值 (Attributes and Values via vars()):[/cyan]")
            try:
                # vars() 会以字典形式展示对象的属性，非常直观
                rprint(vars(result))
            except TypeError:
                # 如果 vars() 不适用，我们回退到使用 dir()
                rprint("[yellow]vars() failed. Falling back to dir() to list all attributes and methods...[/yellow]")
                rprint(dir(result))

            rprint("\n[bold magenta]===================================================================[/bold magenta]\n")

            rprint("[bold green]诊断完成。请复制以上'DIAGNOSTIC MODE'块内的所有内容并回复。程序已终止。[/bold green]")
            return # 诊断完成，直接退出程序

        except Exception as e:
            rprint(f"\n[bold red]爬取过程中发生初始错误，无法进行诊断: {e}[/bold red]")


if __name__ == "__main__":
    asyncio.run(main())