{"timestamp": "2025-08-10T20:30:27.455853", "url": "http://www.qderzhong.net/", "object_type": "<class 'crawl4ai.models.CrawlResultContainer'>", "attributes": {"_results": "[CrawlResult(url='http://www.qderzhong.net/', html='<html class=\"home\" style=\"font-size: 144px;\"><head>\\n  <meta charset=\"utf-8\">\\n  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\">\\n  <link rel=\"apple-touch-icon\" href=\"./favicon.ico\">\\n  <link rel=\"icon\" href=\"./favicon.ico\" type=\"image/x-icon\">\\n  <link rel=\"shortcut icon\" href=\"./favicon.ico\" type=\"image/x-icon\">\\n  <meta name=\"renderer\" content=\"webkit\">\\n  <meta name=\"author\">\\n  <meta name=\"apple-mobile-web-app-capable\" content=\"yes\">\\n  <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"black\">\\n  <meta name=\"format-detection\" content=\"telephone=no,email=no,adress=no\">\\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\\n\\n  <meta name=\"keywords\" content=\"青岛二中\">\\n  <meta name=\"description\" content=\"青岛二中\">\\n\\n  <title>青岛二中</title>\\n  <link href=\"./static/css/jquery.mCustomScrollbar.css\" rel=\"stylesheet\" type=\"text/css\">\\n  <link href=\"./static/css/pc.css\" rel=\"stylesheet\" type=\"text/css\">\\n  <link href=\"./static/css/fix.css\" rel=\"stylesheet\" type=\"text/css\">\\n  <link href=\"./static/css/global.css?v=2.0\" rel=\"stylesheet\" type=\"text/css\">\\n  <link href=\"./static/css/mobile.css\" rel=\"stylesheet\" type=\"text/css\">\\n  <script src=\"./static/js/jquery.js\"></script>\\n  <script src=\"./static/js/jquery.easing.js\"></script>\\n  <script src=\"./static/js/jquery.mousewheel.js\"></script>\\n  <script src=\"./static/js/global.js\"></script>\\n  <script src=\"./static/js/fun.js\"></script>\\n  <script src=\"./static/js/plus/old/winscreen.js\"></script>\\n  <script src=\"./static/js/plus/jquery.mChange.js\"></script>\\n  <script src=\"./static/js/plus/jquery.mScroll.js\"></script>\\n  <script src=\"./static/js/jquery.mCustomScrollbar.js\"></script>\\n  <script src=\"./static/js/iscroll.js\"></script>\\n</head>\\n<body class=\"\">\\n  <link href=\"./static/css/slick.css\" rel=\"stylesheet\" type=\"text/css\">\\n  <style>\\n    @media screen and (max-width:1000px) {\\n      .header2 {\\n        display: none;\\n      }\\n      .body_home .banner {\\n        height: 51vw;\\n      }\\n    }\\n  </style>\\n  <script src=\"./static/js/slick.js\"></script>\\n  <div id=\"alertmask\" style=\"display: none;\">\\n      <div class=\"mailbox\">\\n        <div class=\"box_title\">校长信箱</div>\\n        <div class=\"box_suggest\">\\n          <div>你的建议</div>\\n          <textarea class=\"box_form\" id=\"licenseText\" placeholder=\"在此处输入···\" maxlength=\"300\"></textarea>\\n          <div class=\"textarea_c\"><span id=\"textCount\">0</span>/300</div>\\n        </div>\\n        <div class=\"box_name\">\\n          <div>你的名字</div>\\n          <input class=\"box_form\" id=\"name\" type=\"text\" placeholder=\"请输入你的名字\">\\n        </div>\\n        <div class=\"box_phone\">\\n          <div>你的手机号</div>\\n          <input class=\"box_form\" id=\"phone\" type=\"text\" placeholder=\"请输入您的手机号\">\\n        </div>\\n        <div class=\"box_verify\">\\n          <input class=\"box_form\" id=\"smscode\" type=\"text\">\\n          <div class=\"unsend_gray\">发送验证码</div>\\n          <button class=\"unsend_blue\" onclick=\"sendSms()\">发送验证码</button>\\n          <!-- <div class=\"send_gray\">60s后重新发送</div>  -->\\n        </div>\\n        <div class=\"btn\">\\n          <button class=\"submitbtn\" onclick=\"submit()\">提交</button>\\n          <div class=\"gray_submitbtn\">提交</div>\\n          <div class=\"closebtn\" onclick=\"closeMailBox()\">关闭</div>\\n        </div>\\n      </div>\\n  </div>\\n  <div id=\"alert\" style=\"display: none;\">\\n    <div class=\"msgalert\">\\n      <input class=\"msg fz30\" disabled=\"\">\\n      <button class=\"fz30\" onclick=\"closeMsg()\">确定</button>\\n    </div>\\n  </div>\\n  <script>\\n    var el = document.getElementById(\\'licenseText\\');\\n    var wait = 0;\\n    var yzm = $(\\'.unsend_blue\\') //获取验证码节点\\n    el.addEventListener(\\'input\\',function () {\\n      var len =  txtCount(this); //   调用函数 \\n      document.getElementById(\\'textCount\\').innerHTML = len;\\n    });\\n\\n    //函数 - 获取文本内容和长度\\n    function txtCount(el) {\\n      var val = el.value; \\n      var eLen = val.length; \\n      return eLen;\\n    }\\n\\n    function time() {\\n      if (wait < 0) {\\n        yzm.text(\\'发送验证码\\') //改变文字\\n        yzm.removeAttr(\\'disabled\\') //按钮设置为可点击状态\\n        yzm.css(\\'background\\', \\'#1E65FE\\').css(\\'color\\', \\'white\\')\\n      } else {\\n        yzm.attr(\\'disabled\\', \\'true\\') //开始计时，将按钮设置为不可点击\\n        yzm.text(wait + \\'s后重新发送\\') //改变文字\\n        yzm.css(\\'background\\', \\'#E1E2E6\\').css(\\'color\\', \\'#97989A\\')\\n        wait--\\n        var timer = setTimeout(function() {\\n          clearTimeout(timer)\\n          time()\\n        }, 1000)\\n      }\\n    }\\n\\n    // 验证码\\n    function sendSms(){\\n      $.ajax({\\n        type: \"post\",\\n        contentType: \\'application/json\\',\\n        url: requestUrl + \\'api/portal/Opinion/sms?mobile=\\'+$(\\'#phone\\').val(),\\n        success: function(res){\\n          if(res.code == 0){\\n            closeMailBox();\\n            $(\\'#alert\\').css(\\'display\\', \\'flex\\');\\n            $(\\'.msg\\').val(res.msg);\\n            document.getElementById(\\'textCount\\').innerHTML = 0\\n            return;\\n          }\\n          wait = 60 //设置60秒倒计时\\n          time()\\n        }\\n      })\\n    }\\n    // 提交\\n    function submit(){\\n      var that = $(\\'.submitbtn\\')\\n      that.attr(\\'disabled\\', \\'true\\');\\n      var params = {\\n        \"title\": $(\\'#name\\').val()+\"-\"+$(\\'#phone\\').val(),\\n        \"content\": $(\\'#licenseText\\').val(),\\n        \"user_name\": $(\\'#name\\').val(),\\n        \"mobile_code\": $(\\'#smscode\\').val(),\\n        \"mobile\": $(\\'#phone\\').val()\\n      }\\n      $.ajax({\\n        type: \"post\",\\n        contentType: \\'application/x-www-form-urlencoded\\',\\n        url: requestUrl + \\'api/portal/Opinion/email\\',\\n        data: params,\\n        success: function(res){     \\n          that.removeAttr(\\'disabled\\');   \\n          if(res.msg == \\'ok\\'){\\n            closeMailBox()\\n            $(\\'#alert\\').css(\\'display\\', \\'flex\\');\\n            $(\\'.msg\\').val(\"已提交!\");\\n          }else{\\n            closeMailBox()\\n            $(\\'#alert\\').css(\\'display\\', \\'flex\\');\\n            $(\\'.msg\\').val(res.msg);\\n          }\\n        }\\n      })\\n    }\\n    function closeMsg(){\\n      $(\\'#alert\\').css(\\'display\\', \\'none\\');\\n      if($(\\'.msg\\').val() != \"已提交!\"){\\n        $(\\'.unsend_blue\\').css(\\'display\\', \\'none\\')\\n        $(\\'.unsend_gray\\').css(\\'display\\', \\'flex\\') \\n        $(\\'.submitbtn\\').css(\\'display\\', \\'none\\')\\n        $(\\'.gray_submitbtn\\').css(\\'display\\', \\'flex\\')  \\n        document.getElementById(\\'textCount\\').innerHTML = 0\\n        showMailBox()\\n      }   \\n    }\\n    // 校长信箱的一些判断\\n    $(\".mailbox .box_form\").on(\"input propertychange\",function(){\\n      if($(\\'#licenseText\\').val() && $(\\'#name\\').val() && $(\\'#phone\\').val()){\\n        $(\\'.unsend_blue\\').css(\\'display\\', \\'flex\\')\\n        $(\\'.unsend_gray\\').css(\\'display\\', \\'none\\')        \\n      }else{\\n        $(\\'.unsend_blue\\').css(\\'display\\', \\'none\\')\\n        $(\\'.unsend_gray\\').css(\\'display\\', \\'flex\\')    \\n      }\\n      if($(\\'#licenseText\\').val() && $(\\'#name\\').val() && $(\\'#phone\\').val() && $(\\'#smscode\\').val()){\\n        $(\\'.submitbtn\\').css(\\'display\\', \\'flex\\')\\n        $(\\'.gray_submitbtn\\').css(\\'display\\', \\'none\\')   \\n      }else{\\n        $(\\'.submitbtn\\').css(\\'display\\', \\'none\\')\\n        $(\\'.gray_submitbtn\\').css(\\'display\\', \\'flex\\')   \\n      }\\n    })\\n  </script>\\n  <div class=\"body_home showdiv\">\\n    <!-- 头部 有轮播图 -->\\n    <div class=\"header\">\\n      <div class=\"d2\">\\n        <div class=\"block\">\\n          <div class=\"logo\"><a href=\"./index.html\"><img src=\"./static/images/logoslide.png\" width=\"1932\" height=\"366\"></a></div>\\n          <div class=\"logo1\"><a href=\"./index.html\"><img src=\"./static/images/<EMAIL>\"></a></div>\\n          <div>\\n            <div class=\"tools\">\\n              <div class=\"search\" onclick=\"toshow()\">\\n                <img class=\"search_pc\" src=\"./static/images/search.png\">\\n                <img class=\"search_m\" src=\"./static/images/search-m.png\">\\n                <img class=\"search_m1\" src=\"./static/images/searchblack-m.png\">\\n                <span class=\"searchfont\">搜索</span>              \\n              </div>\\n              <div class=\"tosearch\" style=\"display: none;\">\\n                <input id=\"searchkey\" type=\"text\">\\n                <div class=\"searchpc\" onclick=\"toSearch()\">\\n                  <img src=\"./static/images/searchblue.png\">\\n                  <span class=\"searchfont\">搜索</span>\\n                </div>   \\n                <div class=\"searchm\" onclick=\"toSearch()\">\\n                  <img src=\"./static/images/searchblue-m.png\">\\n                  <span class=\"searchfont\">搜索</span>\\n                </div>         \\n              </div>\\n              <div class=\"column_m\">\\n                <img class=\"column_img\" src=\"./static/images/column-m.png\">\\n                <img class=\"column_img1\" src=\"./static/images/columnblack-m.png\">\\n              </div>\\n              <span class=\"devideline\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</span>\\n              <div class=\"a lang\"></div>\\n            </div>\\n            <script>\\n              function toSearch(){\\n                window.location.href = \\'./search.html?kw=\\'+$(\\'#searchkey\\').val()\\n              }\\n            </script>\\n            <div class=\"nav_main\"></div>         \\n            <!-- 栏目 -->\\n            <script>\\n              $(\".header .nav_main\").on(\"mouseover\",\"a\",function () {\\n                var c_cid = $(this).attr(\\'cid\\');\\n                $(this).css(\\'color\\', \\'#1E65FE\\');\\n                // $(this).find(\"img\")[0].src = \\'./static/images/bluearrow.png\\';\\n                if(!c_cid){\\n                  return;\\n                }\\n                var c_left = $(this).offset().left;\\n                // var p_width = $(\\'.nav_main\\').width();\\n                var p_left = $(\\'.nav_main\\').offset().left;\\n                var hover_child = $(\".header .bar_child\").find(\".item[cid=\\'\"+c_cid+\"\\']\")\\n                // var w_child = $(this).width()\\n                var c_index = $(this).index()\\n                var hover_child_w = $(\".header .bar_child\").find(\".item[cid=\\'\"+c_cid+\"\\']\").width();\\n                hover_child.css(\\'opacity\\', \\'1\\').css(\\'visibility\\', \\'visible\\').css(\\'right\\', \\'30px\\')\\n              });\\n              \\n              $(\".header .nav_main\").on(\"mouseout\",\"a\",function () {\\n                var c_cid = $(this).attr(\\'cid\\');\\n                $(this).css(\\'color\\', \\'white\\');\\n                // $(this).find(\"img\")[0].src = \\'./static/images/downarrow.png\\';\\n                if(!c_cid){\\n                  return;\\n                }\\n                var p_left = $(\\'.nav_main\\').offset().left\\n                var hover_child = $(\".header .bar_child\").find(\".item[cid=\\'\"+c_cid+\"\\']\")\\n                var w_child = $(this).width()\\n                var c_index = $(this).index()\\n                var time = setTimeout(function(){\\n                  clearTimeout(time)\\n                  hover_child.css(\\'opacity\\', \\'0\\').css(\\'visibility\\', \\'hidden\\')\\n                }, 30)\\n                $(\".header .bar_child\").on(\"mouseover\", \".item\", function(){\\n                  clearTimeout(time)\\n                  $(this).css(\\'opacity\\', \\'1\\').css(\\'visibility\\', \\'visible\\')\\n                })\\n                $(\".header .bar_child\").on(\"mouseout\", \".item\", function(){\\n                  $(this).css(\\'opacity\\', \\'0\\').css(\\'visibility\\', \\'hidden\\')\\n                })\\n              });  \\n            </script>   \\n          </div>\\n          <div class=\"bar_child\"></div>\\n        </div>\\n      </div>\\n    </div>\\n    <div class=\"nav_mobile\">\\n      <div>\\n        <div class=\"nav_m_top\">\\n          <div class=\"logo\">\\n            <a href=\"./index.html\">\\n              <img src=\"./static/images/<EMAIL>\" alt=\"\">\\n            </a>\\n          </div>\\n          <div class=\"tools\">\\n            <div style=\"display: flex;\">\\n              <input id=\"searchkey1\" type=\"text\">\\n              <img class=\"nav_search\" src=\"./static/images/searchblue-m.png\" alt=\"\" onclick=\"toshow()\">\\n              <img class=\"nav_search1\" src=\"./static/images/searchblue-m.png\" alt=\"\" onclick=\"toSearchM()\">\\n            </div>      \\n            <img class=\"nav_col\" src=\"./static/images/columnblue-m.png\" alt=\"\">\\n          </div>\\n          <script>\\n            function toSearchM(){\\n              window.location.href = \\'./search.html?kw=\\'+$(\\'#searchkey1\\').val()\\n            }\\n          </script>\\n        </div>\\n        <div class=\"nav_lang\">\\n          <a href=\"./erjidetail.html\" target=\"_blank\"><i>English</i></a>&nbsp;&nbsp;|&nbsp;&nbsp;\\n          <a href=\"./erjidetail.html\" target=\"_blank\"><i>한국어</i></a>\\n        </div>\\n        <div class=\"nav_main_m\">\\n          <div class=\"nav_fm\"></div>\\n          <div class=\"nav_sm\"></div>\\n        </div>\\n        <script>\\n          function toColDetailM(cid, href){\\n            var fchild = $(\".nav_fm\").find(\"[cid=\\'\"+cid+\"\\']\");\\n            var schild = $(\".nav_sm\").find(\".item[cid=\\'\"+cid+\"\\']\");\\n            if(href == \\'#\\') {\\n              event.preventDefault();\\n              fchild.css(\\'color\\', \\'#1E65FE\\').css(\\'fontSize\\', \\'0.28rem\\').css(\\'fontWeight\\', \\'bold\\'); \\n              fchild.find(\\'img\\').attr(\\'src\\', \\'./static/images/bluerightarrow.png\\');\\n              fchild.siblings().css(\\'color\\', \\'#494949\\').css(\\'fontSize\\', \\'0.26rem\\').css(\\'fontWeight\\', \\'normal\\');\\n              fchild.siblings().find(\\'img\\').attr(\\'src\\', \\'./static/images/blackrightarrow.png\\');       \\n              schild.css(\\'display\\', \\'flex\\');\\n              schild.siblings().css(\\'display\\', \\'none\\')\\n            }           \\n          }\\n        </script>\\n      </div>\\n    </div>\\n    <!-- 轮播图 -->\\n    <div class=\"banner\">\\n      <div class=\"frame\"></div>\\n      <div class=\"slide_foot\">\\n        <div class=\"dots\">\\n          <span class=\"a2\">|&nbsp;&nbsp;&nbsp;&nbsp;更多</span>\\n        </div>\\n      </div>  \\n    </div>\\n    <div class=\"bb\"></div>\\n    <div class=\"rr\"><i></i></div>\\n  </div>\\n  <!-- 头部 无轮播图 -->\\n  <div class=\"body_b dis\" tabindex=\"1\">\\n    <div class=\"header2\">\\n      <div class=\"d2\">\\n        <div class=\"block\">\\n          <div class=\"logo\"><a href=\"./index.html\"><img src=\"./static/images/<EMAIL>\" width=\"1288\" height=\"244\"></a></div>\\n          <div>\\n            <div class=\"tools\">\\n              <div class=\"search\" onclick=\"toshow()\">\\n                <img src=\"./static/images/searchblack.png\">\\n                <span>搜索</span>\\n                |\\n              </div>\\n              <div class=\"tosearch\">\\n                <input type=\"text\" id=\"searchkey\">\\n                <div style=\"display: inline-block;\" onclick=\"toSearch()\">\\n                  <img src=\"./static/images/searchblue.png\">\\n                  <span>搜索</span>\\n                </div> \\n              </div>\\n              <div class=\"a lang\">\\n                <a href=\"./erjidetail.html\" target=\"_blank\"><i>English</i></a>\\n                <a href=\"./erjidetail.html\" target=\"_blank\"><i>한국어</i></a>\\n              </div>\\n              <script>\\n                function toSearch(){\\n                  window.location.href = \\'./search.html?kw=\\'+$(\\'#searchkey\\').val()\\n                }\\n              </script>\\n            </div>\\n            <div class=\"nav_main\"></div>      \\n            <script>\\n              $(\".header2 .nav_main\").on(\"mouseover\",\"a\",function () {\\n                var c_cid = $(this).attr(\\'cid\\');\\n                $(this).css(\\'color\\', \\'#1E65FE\\');\\n                // $(this).find(\"img\")[0].src = \\'./static/images/bluearrow.png\\';\\n                if(!c_cid){\\n                  return;\\n                }\\n                var c_left = $(this).offset().left;\\n                // var p_width = $(\\'.nav_main\\').width();\\n                // var p_left = $(\\'.nav_main\\').offset().left;\\n                var hover_child = $(\".header2 .bar_child\").find(\".item[cid=\\'\"+c_cid+\"\\']\")\\n                // var w_child = $(this).width()\\n                var c_index = $(this).index()\\n                var hover_child_w = $(\".header2 .bar_child\").find(\".item[cid=\\'\"+c_cid+\"\\']\").width();\\n                hover_child.css(\\'opacity\\', \\'1\\').css(\\'visibility\\', \\'visible\\').css(\\'right\\', \\'30px\\')\\n              });\\n              \\n              $(\".header2 .nav_main\").on(\"mouseout\",\"a\",function () {\\n                var c_cid = $(this).attr(\\'cid\\');\\n                $(this).css(\\'color\\', \\'#333333\\');\\n                // $(this).find(\"img\")[0].src = \\'./static/images/blackdownarrow.png\\';\\n                if(!c_cid){\\n                  return;\\n                }\\n                var p_left = $(\\'.nav_main\\').offset().left\\n                var hover_child = $(\".header2 .bar_child\").find(\".item[cid=\\'\"+c_cid+\"\\']\")\\n                var w_child = $(this).width()\\n                var c_index = $(this).index()\\n                var time = setTimeout(function(){\\n                  clearTimeout(time)\\n                  hover_child.css(\\'opacity\\', \\'0\\').css(\\'visibility\\', \\'hidden\\')\\n                }, 30)\\n                $(\".header2 .bar_child\").on(\"mouseover\", \".item\", function(){\\n                  clearTimeout(time)\\n                  $(this).css(\\'opacity\\', \\'1\\').css(\\'visibility\\', \\'visible\\')\\n                })\\n                $(\".header2 .bar_child\").on(\"mouseout\", \".item\", function(){\\n                  $(this).css(\\'opacity\\', \\'0\\').css(\\'visibility\\', \\'hidden\\')\\n                })\\n              });        \\n            </script>     \\n          </div>   \\n          <div class=\"bar_child\"></div>\\n        </div>\\n      </div>\\n    </div>\\n    <div class=\"scroll\">\\n      <div class=\"informbox\">\\n        <img class=\"informimg\" src=\"http://img.qderzhong.net/thumb/erzhong/inform.png\" onclick=\"showInform()\">\\n        <div class=\"inform_content\"></div>\\n      </div>\\n      <script>\\n        var isshow;\\n        if(!isMobileM()){\\n          isshow = true;\\n        } else {\\n          isshow = false;\\n        }   \\n        function showInform(){\\n          if(isshow){\\n            closeInform()\\n          }else{\\n            isshow = true;\\n            $(\\'.informbox .inform_content\\').css(\\'display\\', \\'block\\');\\n            if(!isMobileM()){         \\n              $(\\'.informimg\\').css(\\'right\\', \\'unset\\').css(\\'left\\', \\'-0.2rem\\').css(\\'transition\\', \\'none\\');\\n            } else {\\n              $(\\'.informbox\\').css(\\'width\\', \\'1.85rem\\');\\n              $(\\'.informbox\\').css(\\'height\\', \\'1.59rem\\');\\n              $(\\'.informimg\\').css(\\'right\\', \\'unset\\').css(\\'left\\', \\'-0.8rem\\').css(\\'transition\\', \\'none\\').css(\\'top\\', \\'-0.2rem\\');\\n            }\\n          }    \\n        }\\n        function toAnnDetail(id){\\n          window.location.href=\\'./erjidetail.html?id=\\'+id\\n        }\\n      </script>\\n      <!-- <div class=\"home_first\">       \\n        <div class=\"content\">\\n          <div class=\"box\">\\n            <div class=\"rightIntro\"></div> \\n            <div class=\"leftIntro\"></div>\\n          </div>\\n        </div>\\n        <div class=\"title\">\\n          <div class=\"top\">INTRODUCTION</div>\\n          <div class=\"middle\">二中介绍</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n      </div> -->\\n      <div class=\"home_second\">\\n        <div class=\"title\">\\n          <div class=\"top\">EVENTS</div>\\n          <div class=\"middle\">二中要闻</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n        <div class=\"events\">\\n          <div class=\"detail\" onclick=\"toDetail()\"></div>        \\n          <div class=\"eventlist\"></div>\\n          <div>\\n            <div class=\"spec_m_event\" style=\"background: #eeeeee;\"></div>\\n            <div class=\"eventlist_m\"></div>\\n          </div>\\n          <script>       \\n          if(!isMobileM()){\\n            $(\\'.eventlist .item:first-child\\').addClass(\\'active\\')\\n            var itemActive = 0;\\n            var interval = setInterval(function(){\\n              var item = $(\".eventlist .item\");\\n              item.removeClass(\"active\");\\n              var select = item.eq(++itemActive % 5)\\n              select.addClass(\"active\");\\n              $(\\'.easy_detail .stitle\\').text(item[itemActive%5].innerText.split(\\'\\\\n\\')[0]);  \\n              $(\\'.easy_detail .sdetail\\').text(item[itemActive%5].children[0].children[1].getAttribute(\\'excerpt\\'))\\n              $(\\'.detail img\\').attr(\\'src\\', item[itemActive%5].children[1].children[0].src)\\n              $(\\'.detail\\').attr(\\'newid\\', item[itemActive%5].getAttribute(\\'newid\\'))\\n              $(\\'.detail\\').attr(\\'islink\\', item[itemActive%5].getAttribute(\\'islink\\'))\\n              \\n            }, 1000);\\n            $(\".eventlist\").on(\"mouseover\",\".item\",function () {\\n              clearInterval(interval)\\n              itemActive = $(this).index();\\n              var obj = $(this)[0];\\n              $(\\'.easy_detail .stitle\\').text(obj.innerText.split(\\'\\\\n\\')[0]);\\n              $(\\'.easy_detail .sdetail\\').text(obj.children[0].children[1].getAttribute(\\'excerpt\\'))\\n              $(\\'.detail img\\').attr(\\'src\\', obj.children[1].children[0].src)\\n              $(\\'.detail\\').attr(\\'newid\\', obj.getAttribute(\\'newid\\'))\\n              $(\\'.detail\\').attr(\\'islink\\', obj.getAttribute(\\'islink\\'))\\n            })\\n\\n            $(\".eventlist\").on(\"mouseout\",\".item\",function () {\\n              $(\".eventlist .item\").removeClass(\"active\");\\n              interval = setInterval(function(){         \\n                var item = $(\".eventlist .item\");\\n                item.removeClass(\"active\");\\n                var select = item.eq(++itemActive % 5)\\n                select.addClass(\"active\");\\n                $(\\'.easy_detail .stitle\\').text(item[itemActive%5].innerText.split(\\'\\\\n\\')[0]);     \\n                $(\\'.easy_detail .sdetail\\').text(item[itemActive%5].children[0].children[1].getAttribute(\\'excerpt\\'))\\n                $(\\'.detail img\\').attr(\\'src\\', item[itemActive%5].children[1].children[0].src)\\n                $(\\'.detail\\').attr(\\'newid\\', item[itemActive%5].getAttribute(\\'newid\\'))\\n                $(\\'.detail\\').attr(\\'islink\\', item[itemActive%5].getAttribute(\\'islink\\'))\\n              }, 1000);\\n            })\\n          }  \\n          function toDetail(){\\n            if($(\\'.events .detail\\').attr(\\'islink\\') != \"0\"){\\n              window.location.href=$(\\'.events .detail\\').attr(\\'newid\\')\\n            }else{\\n              console.log($(\\'.events .detail\\').attr(\\'newid\\'))\\n              window.location.href=\\'./erjidetail.html?id=\\' + $(\\'.events .detail\\').attr(\\'newid\\')\\n            }\\n          }\\n          function toMsgDetail(islink, link){\\n            if(islink != 0){\\n              window.location.href=link\\n            }else{\\n              window.location.href=\\'./erjidetail.html?id=\\' + link\\n            }\\n          }\\n          </script>\\n        </div>    \\n        <div class=\"blueback\"></div>\\n        <div class=\"greenback\"></div>    \\n      </div>\\n      <div class=\"home_third\">\\n        <script>\\n          function toTeaDetail(islink, link){\\n            if(islink != 0){\\n              window.location.href=link\\n            }else{\\n              window.location.href=\\'./erjidetail.html?id=\\' + link\\n            }\\n          }\\n          function toStuDetail(islink, link){\\n            if(islink != 0){\\n              window.location.href=link\\n            }else{\\n              window.location.href=\\'./erjidetail.html?id=\\' + link\\n            }\\n          }\\n        </script>\\n        <div class=\"title_top\">\\n          <div class=\"lt\">\\n            <div class=\"l\">TEACHERS</div>\\n            <div class=\"r\">二中名师</div>\\n          </div>\\n          <div class=\"colomnline\"></div>\\n          <div class=\"rt\">优秀教师大批涌现，教师发展硕果累累</div>\\n        </div>\\n        <div class=\"title\">\\n          <div class=\"top\">TEACHERS</div>\\n          <div class=\"middle\">二中名师</div>\\n          <div class=\"bottom\"></div>\\n        </div>  \\n        <div class=\"allpeo\">\\n          <div class=\"famousteacher\">\\n            <div class=\"sometea\"></div>\\n          </div>\\n        </div>\\n      </div>\\n      <div class=\"home_third_s\">\\n        <div class=\"title_top\">\\n          <div class=\"lt\">\\n            <div class=\"l\">STUDENTS</div>\\n            <div class=\"r\">二中学子</div>\\n          </div>\\n          <div class=\"colomnline\"></div>\\n          <div class=\"rt\">“全人发展”课程，搭建学生展翅高飞的天空</div>\\n        </div>\\n        <div class=\"title\">\\n          <div class=\"top\">STUDENTS</div>\\n          <div class=\"middle\">二中学子</div>\\n          <div class=\"bottom\"></div>\\n        </div> \\n        <div class=\"allpeo\">\\n          <div class=\"student\">\\n            <div class=\"somestu\"></div>         \\n          </div>\\n        </div>\\n        <script>\\n          if(isMobileM()){\\n            $(\\'.stuintro .no\\').after($(\\'.stuintro .jointime\\'))\\n          }\\n        </script>\\n      </div>\\n      <div class=\"home_forth\">\\n        <div class=\"title_top\">\\n          <div class=\"lt\">\\n            <div class=\"l\">VIDEO</div>\\n            <div class=\"r\">宣传视频</div>\\n          </div>\\n          <div class=\"colomnline\"></div>\\n          <div class=\"rt\">青岛二中是一所有近百年历史的老牌名校，历年的入学录取成绩、高考成绩、竞赛成绩都保持青岛市第一</div>\\n        </div>\\n        <script type=\"text/javascript\">  \\n          \\n          // 视频播放 \\n          function toplay(){\\n            var videos = document.getElementsByTagName(\\'video\\')\\n            for (let i =0;i<videos.length;i++){\\n              videos[i].addEventListener(\\'play\\', function(){\\n                var vs = document.getElementsByTagName(\\'video\\');\\n                for (let j = 0; j < vs.length; j++) {\\n                  if (vs[j]!==this) vs[j].pause();\\n                }\\n              })\\n            }\\n            \\n            var that = event.target;  \\n            if($(that)[0].paused){\\n              $(that).trigger(\\'play\\');\\n              $(that).prev().css(\\'display\\', \\'none\\')\\n            } else {\\n              $(that).prev().css(\\'display\\', \\'flex\\')\\n              $(that).trigger(\\'pause\\');\\n            }\\n            // 监听是否暂停\\n            $(that)[0].addEventListener(\\'pause\\', function(){\\n              $(this).prev().css(\\'display\\', \\'flex\\')\\n            })\\n          }\\n        </script>\\n        <div class=\"video_c\">\\n          <div class=\"top\"></div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n        <!-- 手机 -->\\n        <div class=\"title\">\\n          <div class=\"top\">VIDEO</div>\\n          <div class=\"middle\">宣传视频</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n        <div>\\n          <div class=\"spec_m_video\"></div>\\n          <div class=\"videolist_m\"></div>\\n        </div>\\n      </div>\\n      \\n      <!-- <div class=\"home_fifth\">\\n        <div class=\"title\">\\n          <div class=\"top\">EVENTS</div>\\n          <div class=\"middle\">快速通道</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n        <div class=\"passage\">\\n          <div class=\"line\">\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/educate.png\">\\n              <span>教育悟语</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/famousteacher.png\">\\n              <span>名师讲堂</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/homeschool.png\">\\n              <span>家长学校</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/speech.png\">\\n              <span>国旗下演讲</span>\\n            </div>\\n          </div>\\n          <div class=\"line\">\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/magazine.png\">\\n              <span>校报校刊</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/schoolaffairs.png\">\\n              <span>校务公开</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/channel.png\">\\n              <span>内网通道</span>\\n            </div>\\n            <div onclick=\"showMailBox()\">\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/mailbox.png\">\\n              <span>校长信箱</span>\\n            </div>\\n          </div>\\n        </div>\\n      </div> -->\\n      <!-- 底部footer -->\\n      <div class=\"footer-b\">\\n        <div class=\"part-t\">\\n          <div class=\"left-nav\"></div>\\n          <div class=\"nav—pic\">\\n            <img src=\"./static/images/qrcode.png\">\\n            <a onclick=\"showMailBox()\" class=\"part-b-mail-box mailbox-m\">校长邮箱入口</a>\\n          </div>\\n          \\n        </div>\\n        <div class=\"part-b2 pc\">\\n          <div>\\n            <a href=\"#\">友情链接：</a>\\n            <a href=\"http://edu.qingdao.gov.cn/\" target=\"_blank\">青岛市教育局</a>\\n          </div>\\n        </div>\\n        <div class=\"part-b pc\">\\n          <div>\\n            <a href=\"#\">版权所有©山东省青岛第二中学 <img src=\"./static/images/banquan.png\" style=\"margin-left: 3px;\"></a>\\n            <a href=\"https://beian.miit.gov.cn/#/Integrated/index\" target=\"_blank\" style=\"padding-left: 29px;padding-right: 21px;\">备案序号：鲁ICP备10005652号-2</a>\\n            <a href=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=37021202000507\" target=\"_blank\" style=\"padding-left: 29px;\"><img src=\"./static/images/record.png\">鲁公网安备：37021202000507号</a>\\n            <a onclick=\"showMailBox()\" class=\"part-b-mail-box\">校长邮箱入口</a>\\n          </div> \\n        </div>      \\n        <div class=\"part-b mobile\">\\n          <div>\\n            <a href=\"http://edu.qingdao.gov.cn/\" target=\"_blank\">青岛市教育局</a>\\n            <a href=\"https://beian.miit.gov.cn/#/Integrated/index\" target=\"_blank\" style=\"padding-left: 29px;padding-right: 21px;\">鲁ICP备10005652号-2</a>\\n            <a href=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=37021202000507\" target=\"_blank\" style=\"padding-left: 29px;\"><img src=\"./static/images/record.png\">鲁公网安备：37021202000507号</a>\\n            <a href=\"#\"><img src=\"./static/images/banquan.png\" style=\"margin-right: 3px;\">版权所有©山东省青岛第二中学</a>\\n          </div>          \\n        </div>\\n      </div>\\n    </div>\\n  </div>\\n  <script type=\"text/javascript\" src=\"./static/js/ajax/homeajax.js?v=1.0\"></script>\\n  <script src=\"./static/js/plus/jquery.imgpreload.js\"></script>\\n  <script>\\n    var first = 0;\\n\\n    setTimeout(function () {\\n      $(\".banner .slick-active .a1\").focus();\\n    }, 500);\\n\\n    $(function () {\\n      $(\".body_home\").addClass(\"showdiv\");\\n      setTimeout(function () {\\n        $(\".body_home\").addClass(\"s\");\\n      }, 500);\\n    });\\n\\n    $(\".rr\").click(function () {\\n      $(\".body_r\").removeClass(\"dis\");\\n      $(\\'.body_r img\\').each(function (i) {\\n        if (!$(this).attr(\\'src\\')) {\\n          $(this).attr(\\'src\\', $(this).attr(\\'data-src\\'))\\n          $(this).removeAttr(\\'data-src\\');\\n        }\\n      })\\n      setdataL();\\n\\n      $(\".body_b\").css(\"z-index\", 1);\\n      $(\".body_r\").css(\"z-index\", 3);\\n      $(\".body_r\").addClass(\"showdiv\");\\n      $(\".nav_b\").addClass(\"showdiv\");\\n      $(\".rr\").removeClass(\"showdiv\");\\n      $(\".home\").addClass(\"lock\");\\n      setTimeout(function () {\\n        $(\".body_home\").removeClass(\"showdiv\");\\n      }, 500);\\n      setTimeout(function () {\\n        first = 1;\\n      }, 1000);\\n    });\\n\\n    $(\".bb\").click(function () {\\n      $(\".body_b\").addClass(\"showdiv\");\\n      $(\".header\").addClass(\"mobile\");\\n      setTimeout(function () {\\n        $(\".body_home\").removeClass(\"showdiv\");\\n        $(\".home_first\").addClass(\"showdiv\");\\n      }, 500);\\n    })\\n\\n    $(\".rb\").click(function () {\\n      setTimeout(function () {\\n        $(\".ll\").click();\\n      }, 1100);\\n      if ($(\".body_b\").hasClass(\"showdiv\")) {\\n        $(\".body_r\").removeClass(\"showdiv\");\\n      }\\n      else {\\n        $(\".bb\").click();\\n        $(\".body_b\").css(\"z-index\", 3);\\n        $(\".body_r\").css(\"z-index\", 1);\\n      }\\n    });\\n\\n    $(\".body_b\").scrollTop(0);\\n\\n    $(\".body_home\").mousewheel(function (event, delta, deltaX, deltaY) {\\n      if(!isMobileM()){\\n        // 通知公告延时1分钟关闭\\n        setTimeout(function(){\\n          $(\\'.informbox .inform_content\\').css(\\'display\\', \\'none\\');\\n          $(\\'.informimg\\').css(\\'right\\', \\'0\\').css(\\'left\\', \\'unset\\').css(\\'transition\\', \\'none\\')\\n          $(\\'.informbox\\').css(\\'width\\', \\'1.85rem\\');\\n          $(\\'.informbox\\').css(\\'height\\', \\'1.59rem\\');\\n        }, 60000)\\n      }\\n      \\n      if (delta < 0) {\\n        $(\".bb\").click();\\n      }\\n    });\\n\\n    var sw_k = 1;\\n    document.onkeydown = function (event) {\\n      var e = event || window.event || arguments.callee.caller.arguments[0];\\n      if ((e.keyCode == 40 || e.keyCode == 32 || e.keyCode == 34) && $(\".body_home\").hasClass(\"showdiv\")) {\\n        sw_k = 0;\\n        $(\".bb\").click();\\n        $(\".body_b\").animate({ scrollTop: 0 }, 10);\\n        $(\".body_b\").focus();\\n        setTimeout(function () { sw_k = 1; }, 1000);\\n      }\\n      if ((e.keyCode == 38 || e.keyCode == 33) && $(\".body_b\").hasClass(\"showdiv\") && $(\".body_b\").scrollTop() == 0) {\\n        sw_k = 0;\\n        $(\".body_home\").addClass(\"showdiv\").focus();\\n        setTimeout(function () {\\n          $(\".banner .slick-active .a1\").focus();\\n        }, 500);\\n        $(\".body_b\").removeClass(\"showdiv\");\\n        $(\".home_first\").removeClass(\"showdiv\");\\n        if ($(\".body_b\").css(\"position\") == \"fixed\") $(\".header\").removeClass(\"mobile\");\\n        setTimeout(function () { sw_k = 1; }, 1000);\\n      }\\n      if ((e.keyCode == 37 || e.keyCode == 38 || e.keyCode == 33) && $(\".body_r\").hasClass(\"showdiv\") && sw == 1) {\\n        $(\".nav_b .list .item.now\").prev().click();\\n      }\\n      if ((e.keyCode == 39 || e.keyCode == 40 || e.keyCode == 34 || e.keyCode == 32) && $(\".body_r\").hasClass(\"showdiv\") && sw == 1) {\\n        $(\".nav_b .list .item.now\").next().click();\\n      }\\n    }\\n\\n    $(window).scroll(function () {\\n      toHidden()\\n      if ($(window).width() <= 850) {\\n        if ($(window).scrollTop() < 50) {\\n          $(\".header\").removeClass(\"mobile\")\\n        }\\n        else {\\n          $(\".header\").addClass(\"mobile\")\\n        }\\n      }\\n    });\\n\\n    $(\".ll\").click(function () {\\n      $(\".body_r\").removeClass(\"showdiv\");\\n      $(\".nav_b\").removeClass(\"showdiv\");\\n      $(\".rr\").addClass(\"showdiv\");\\n      $(\".home\").removeClass(\"lock\")\\n      setTimeout(function () {\\n        $(\".nav_b .item:first\").click();\\n        $(\".home1\").removeClass(\"showdiv\");\\n        if (!$(\".body_b\").hasClass(\"showdiv\")) {\\n          $(\".body_home\").addClass(\"showdiv\").focus();\\n        }\\n      }, 1100);\\n    });\\n    function closeInform(){\\n      isshow = false;\\n      $(\\'.informbox .inform_content\\').css(\\'display\\', \\'none\\'); \\n      if(!isMobileM()){\\n        $(\\'.informbox\\').css(\\'width\\', \\'1.85rem\\');\\n        $(\\'.informbox\\').css(\\'height\\', \\'1.59rem\\');\\n        $(\\'.informimg\\').css(\\'right\\', \\'0\\').css(\\'left\\', \\'unset\\').css(\\'transition\\', \\'none\\');\\n      } else {\\n        $(\\'.informimg\\').css(\\'right\\', \\'-2.5rem\\').css(\\'left\\', \\'unset\\').css(\\'transition\\', \\'none\\');\\n      }\\n    }\\n    $(\".body_b\").scroll(function () {\\n      toHidden()\\n      $(\".home_first,.home_third, .home_third_s, .home_forth,.home_fifth,.home_second\").each(function (index, element) {\\n        var e = $(this);\\n        var fix = parseInt(e.attr(\"fix\"));\\n        if (!fix && fix != 0) { fix = $(window).height() * 6 * 0.1; }\\n        else { fix = $(window).height() * fix * 0.1; }\\n        if ($(window).scrollTop() >= $(e).offset().top - fix) {\\n          if (!$(e).hasClass(\"showdiv\")) {\\n            $(e).addClass(\"showdiv\");\\n          }\\n        }\\n        else {\\n          if ($(e).hasClass(\"showdiv\")) {\\n            $(e).removeClass(\"showdiv\");\\n          }\\n        }\\n      });\\n    });\\n\\n    $(\".body_b\").mousewheel(function (event, delta, deltaX, deltaY) {\\n      toHidden()\\n      if (delta > 0 && $(\".body_b\").scrollTop() == 0) {\\n        $(\".body_home\").addClass(\"showdiv\");\\n        setTimeout(function () {\\n          if ($(window).width() > 850) { $(\".banner .slick-active .a1\").focus(); }\\n        }, 500);\\n        $(\".body_b\").removeClass(\"showdiv\");\\n        $(\".home_first\").removeClass(\"showdiv\");\\n        if ($(\".body_b\").css(\"position\") == \"fixed\") $(\".header\").removeClass(\"mobile\");\\n      }\\n\\n      if (deltaY < 0 && $(\".body_b\").scrollTop() + $(window).height() >= $(\".body_b .scroll\").height()) {\\n      }\\n      if (deltaY > 0) {\\n        $(\".header2\").removeClass(\"lit\");\\n      }\\n      if (deltaY < 0) {\\n        $(\".header2\").addClass(\"lit\");\\n      }\\n    });\\n\\n    //延迟加载\\n    var sto_r = 0;\\n    var loaded = 0;\\n\\n    //获取图片地址\\n    var imgArr = [];\\n    $(\\'.banner .child\\').each(function () {\\n      var item = $(this);\\n      if (typeof (item.attr(\"src\")) != \"undefined\" && item.attr(\"src\") != \"\" && this.nodeName.toLowerCase() == \"img\") {\\n        imgArr.push({\\n          src: item.attr(\\'src\\')\\n        });\\n      } else if (item.css(\"background-image\") != \"none\") {\\n        imgArr.push({\\n          src: item.css(\"background-image\").replace(/^url\\\\([\"\\']?/, \\'\\').replace(/[\"\\']?\\\\)$/, \\'\\')\\n        });\\n      }\\n    });\\n\\n    function _lazyload() {\\n      $(\".body_b img,.body_b source\").each(function (index, element) {\\n        if ($(this).attr(\"data-src\") != \"\" && $(this).attr(\"data-src\") != undefined) {\\n          $(this).attr(\"src\", $(this).attr(\"data-src\"));\\n          $(this).removeAttr(\\'data-src\\');\\n        }\\n      });\\n    }\\n\\n    //加载图片\\n    $.imgpreload(imgArr, {\\n      each: function () { },\\n      all: function () {\\n        $(\".body_b\").removeClass(\"dis\");\\n        $(\".rr\").addClass(\"showdiv\");\\n      }\\n    });\\n\\n    window.onload = function () {\\n      _lazyload();\\n    }\\n\\n  </script>\\n\\n\\n</body></html>', fit_html='<html class=\"home\" style=\"font-size: 144px;\"><body class=\"\">\\n  <link type=\"text/css\"><div id=\"alertmask\">\\n      <div class=\"mailbox\">\\n        <div class=\"box_title\">校长信箱</div>\\n        <div class=\"box_suggest\">\\n          <div>你的建议</div>\\n          <textarea class=\"box_form\" id=\"licenseText\"></textarea><div class=\"textarea_c\"><span id=\"textCount\">0</span>/300</div>\\n        </div>\\n        <div class=\"box_name\">\\n          <div>你的名字</div>\\n          <input class=\"box_form\" id=\"name\" type=\"text\"></div>\\n        <div class=\"box_phone\">\\n          <div>你的手机号</div>\\n          </div>\\n        <div class=\"box_verify\">\\n          <div class=\"unsend_gray\">发送验证码</div>\\n          <button class=\"unsend_blue\">发送验证码</button>\\n          \\n        </div>\\n        <div class=\"btn\">\\n          <button class=\"submitbtn\">提交</button>\\n          <div class=\"gray_submitbtn\">提交</div>\\n          <div class=\"closebtn\">关闭</div>\\n        </div>\\n      </div>\\n  </div>\\n  <div id=\"alert\">\\n    <div class=\"msgalert\">\\n      <input class=\"msg fz30\"><button class=\"fz30\">确定</button>\\n    </div>\\n  </div>\\n  <div class=\"body_home showdiv\">\\n    \\n    <div class=\"header\">\\n      <div class=\"d2\">\\n        <div class=\"block\">\\n          <div class=\"logo\"><a><img></a></div>\\n          <div class=\"logo1\"><a><img></a></div>\\n          <div>\\n            <div class=\"tools\">\\n              <div class=\"search\">\\n                <img class=\"search_pc\"><img class=\"search_m\"><img class=\"search_m1\"><span class=\"searchfont\">搜索</span>              \\n              </div>\\n              <div class=\"tosearch\">\\n                <input id=\"searchkey\" type=\"text\"><div class=\"searchpc\">\\n                  <img></div>   \\n                <div class=\"searchm\">\\n                  <img></div>         \\n              </div>\\n              <div class=\"column_m\">\\n                <img class=\"column_img\"><img class=\"column_img1\"></div>\\n              <span class=\"devideline\">\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0|</span>\\n              <div class=\"a lang\"></div>\\n            </div>\\n            <div class=\"nav_main\"></div>         \\n            \\n            </div>\\n          <div class=\"bar_child\"></div>\\n        </div>\\n      </div>\\n    </div>\\n    <div class=\"nav_mobile\">\\n      <div>\\n        <div class=\"nav_m_top\">\\n          <div class=\"logo\">\\n            <a>\\n              <img></a>\\n          </div>\\n          <div class=\"tools\">\\n            <div>\\n              <input id=\"searchkey1\" type=\"text\"><img class=\"nav_search\"><img class=\"nav_search1\"></div>      \\n            <img class=\"nav_col\"></div>\\n          </div>\\n        <div class=\"nav_lang\">\\n          <a><i>English</i></a>\\xa0\\xa0|\\xa0\\xa0\\n          <a><i>한국어</i></a>\\n        </div>\\n        <div class=\"nav_main_m\">\\n          <div class=\"nav_fm\"></div>\\n          <div class=\"nav_sm\"></div>\\n        </div>\\n        </div>\\n    </div>\\n    \\n    <div class=\"banner\">\\n      <div class=\"frame\"></div>\\n      <div class=\"slide_foot\">\\n        <div class=\"dots\">\\n          <span class=\"a2\">|\\xa0\\xa0\\xa0\\xa0更多</span>\\n        </div>\\n      </div>  \\n    </div>\\n    <div class=\"bb\"></div>\\n    <div class=\"rr\"><i></i></div>\\n  </div>\\n  \\n  <div class=\"body_b dis\">\\n    <div class=\"header2\">\\n      <div class=\"d2\">\\n        <div class=\"block\">\\n          <div>\\n            <div class=\"tools\">\\n              <div class=\"search\">\\n                <img><span>搜索</span>\\n                |\\n              </div>\\n              <div class=\"tosearch\">\\n                <input type=\"text\" id=\"searchkey\"><div>\\n                  <img><span>搜索</span>\\n                </div> \\n              </div>\\n              <div class=\"a lang\">\\n                <a><i>English</i></a>\\n                <a><i>한국어</i></a>\\n              </div>\\n              </div>\\n            </div>   \\n          </div>\\n      </div>\\n    </div>\\n    <div class=\"scroll\">\\n      <div class=\"informbox\">\\n        <img class=\"informimg\"><div class=\"inform_content\"></div>\\n      </div>\\n      <div class=\"home_second\">\\n        <div class=\"title\">\\n          <div class=\"top\">EVENTS</div>\\n          <div class=\"middle\">二中要闻</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n        <div class=\"events\">\\n          <div class=\"detail\"></div>        \\n          <div class=\"eventlist\"></div>\\n          <div>\\n            <div class=\"spec_m_event\"></div>\\n            <div class=\"eventlist_m\"></div>\\n          </div>\\n          </div>    \\n        <div class=\"blueback\"></div>\\n        <div class=\"greenback\"></div>    \\n      </div>\\n      <div class=\"home_third\">\\n        <div class=\"title_top\">\\n          <div class=\"lt\">\\n            <div class=\"l\">TEACHERS</div>\\n            <div class=\"r\">二中名师</div>\\n          </div>\\n          <div class=\"colomnline\"></div>\\n          <div class=\"rt\">优秀教师大批涌现，教师发展硕果累累</div>\\n        </div>\\n        <div class=\"title\">\\n          <div class=\"top\">TEACHERS</div>\\n          <div class=\"middle\">二中名师</div>\\n          </div>  \\n        <div class=\"allpeo\">\\n          <div class=\"famousteacher\">\\n            <div class=\"sometea\"></div>\\n          </div>\\n        </div>\\n      </div>\\n      <div class=\"home_third_s\">\\n        <div class=\"title_top\">\\n          <div class=\"lt\">\\n            <div class=\"l\">STUDENTS</div>\\n            <div class=\"r\">二中学子</div>\\n          </div>\\n          <div class=\"rt\">“全人发展”课程，搭建学生展翅高飞的天空</div>\\n        </div>\\n        <div class=\"title\">\\n          <div class=\"top\">STUDENTS</div>\\n          <div class=\"middle\">二中学子</div>\\n          </div> \\n        <div class=\"allpeo\">\\n          <div class=\"student\">\\n            <div class=\"somestu\"></div>         \\n          </div>\\n        </div>\\n        </div>\\n      <div class=\"home_forth\">\\n        <div class=\"title_top\">\\n          <div class=\"lt\">\\n            <div class=\"l\">VIDEO</div>\\n            <div class=\"r\">宣传视频</div>\\n          </div>\\n          <div class=\"rt\">青岛二中是一所有近百年历史的老牌名校，历年的入学录取成绩、高考成绩、竞赛成绩都保持青岛市第一</div>\\n        </div>\\n        <div class=\"video_c\">\\n          <div class=\"top\"></div>\\n          </div>\\n        \\n        <div class=\"title\">\\n          <div class=\"top\">VIDEO</div>\\n          <div class=\"middle\">宣传视频</div>\\n          </div>\\n        <div>\\n          <div class=\"spec_m_video\"></div>\\n          <div class=\"videolist_m\"></div>\\n        </div>\\n      </div>\\n      \\n      \\n      \\n      <div class=\"footer-b\">\\n        <div class=\"part-t\">\\n          <div class=\"left-nav\"></div>\\n          <div class=\"nav—pic\">\\n            <img><a class=\"part-b-mail-box mailbox-m\">校长邮箱入口</a>\\n          </div>\\n          \\n        </div>\\n        <div class=\"part-b2 pc\">\\n          <div>\\n            <a>友情链接：</a>\\n            <a>青岛市教育局</a>\\n          </div>\\n        </div>\\n        <div class=\"part-b pc\">\\n          <div>\\n            <a>版权所有©山东省青岛第二中学 <img></a>\\n            <a>备案序号：鲁ICP备10005652号-2</a>\\n            <a><img>鲁公网安备：37021202000507号</a>\\n            <a class=\"part-b-mail-box\">校长邮箱入口</a>\\n          </div> \\n        </div>      \\n        <div class=\"part-b mobile\">\\n          <div>\\n            <a>青岛市教育局</a>\\n            <a>鲁ICP备10005652号-2</a>\\n            <a><img>鲁公网安备：37021202000507号</a>\\n            <a><img>版权所有©山东省青岛第二中学</a>\\n          </div>          \\n        </div>\\n      </div>\\n    </div>\\n  </div>\\n  </body></html>', success=True, cleaned_html='<html>\\n<head>\\n  <title>青岛二中</title>\\n  </head>\\n<body>\\n  <div>\\n      <div>\\n        <div>校长信箱</div>\\n        <div>\\n          <div>你的建议</div>\\n          <div>\\n<span>0</span>/300</div>\\n        </div>\\n        <div>\\n          <div>你的名字</div>\\n          <input>\\n        </div>\\n        <div>\\n          <div>你的手机号</div>\\n          <input>\\n        </div>\\n        <div>\\n          <input>\\n          <div>发送验证码</div>\\n          <button>发送验证码</button>\\n          <!-- <div class=\"send_gray\">60s后重新发送</div>  -->\\n        </div>\\n        <div>\\n          <button>提交</button>\\n          <div>提交</div>\\n          <div>关闭</div>\\n        </div>\\n      </div>\\n  </div>\\n  <div>\\n    <div>\\n      <input>\\n      <button>确定</button>\\n    </div>\\n  </div>\\n  <div>\\n    <!-- 头部 有轮播图 -->\\n    <div>\\n      <div>\\n        <div>\\n          <div><a href=\"./index.html\"><img src=\"./static/images/logoslide.png\" width=\"1932\" height=\"366\"></a></div>\\n          <div><a href=\"./index.html\"><img src=\"./static/images/<EMAIL>\"></a></div>\\n          <div>\\n            <div>\\n              <div>\\n                <img src=\"./static/images/search.png\">\\n                <img src=\"./static/images/search-m.png\">\\n                <img src=\"./static/images/searchblack-m.png\">\\n                <span>搜索</span>              \\n              </div>\\n              <div>\\n                <input>\\n                <div>\\n                  <img src=\"./static/images/searchblue.png\">\\n                  <span>搜索</span>\\n                </div>   \\n                <div>\\n                  <img src=\"./static/images/searchblue-m.png\">\\n                  <span>搜索</span>\\n                </div>         \\n              </div>\\n              <div>\\n                <img src=\"./static/images/column-m.png\">\\n                <img src=\"./static/images/columnblack-m.png\">\\n              </div>\\n              <span>\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0\\xa0|</span>\\n              </div>\\n            <!-- 栏目 -->\\n            </div>\\n          </div>\\n      </div>\\n    </div>\\n    <div>\\n      <div>\\n        <div>\\n          <div>\\n            <a href=\"./index.html\">\\n              <img src=\"./static/images/<EMAIL>\" alt=\"\">\\n            </a>\\n          </div>\\n          <div>\\n            <div>\\n              <input>\\n              <img src=\"./static/images/searchblue-m.png\" alt=\"\">\\n              <img src=\"./static/images/searchblue-m.png\" alt=\"\">\\n            </div>      \\n            <img src=\"./static/images/columnblue-m.png\" alt=\"\">\\n          </div>\\n          </div>\\n        <div>\\n          <a href=\"./erjidetail.html\"><i>English</i></a>\\xa0\\xa0|\\xa0\\xa0\\n          <a href=\"./erjidetail.html\"><i>한국어</i></a>\\n        </div>\\n        </div>\\n    </div>\\n    <!-- 轮播图 -->\\n    <div>\\n      <div>\\n        <div>\\n          <span>|\\xa0\\xa0\\xa0\\xa0更多</span>\\n        </div>\\n      </div>  \\n    </div>\\n    </div>\\n  <!-- 头部 无轮播图 -->\\n  <div>\\n    <div>\\n      <div>\\n        <div>\\n          <div><a href=\"./index.html\"><img src=\"./static/images/<EMAIL>\" width=\"1288\" height=\"244\"></a></div>\\n          <div>\\n            <div>\\n              <div>\\n                <img src=\"./static/images/searchblack.png\">\\n                <span>搜索</span>\\n                |\\n              </div>\\n              <div>\\n                <input>\\n                <div>\\n                  <img src=\"./static/images/searchblue.png\">\\n                  <span>搜索</span>\\n                </div> \\n              </div>\\n              <div>\\n                <a href=\"./erjidetail.html\"><i>English</i></a>\\n                <a href=\"./erjidetail.html\"><i>한국어</i></a>\\n              </div>\\n              </div>\\n            </div>   \\n          </div>\\n      </div>\\n    </div>\\n    <div>\\n      <div>\\n        <img src=\"http://img.qderzhong.net/thumb/erzhong/inform.png\">\\n        </div>\\n      <!-- <div class=\"home_first\">       \\n        <div class=\"content\">\\n          <div class=\"box\">\\n            <div class=\"rightIntro\"></div> \\n            <div class=\"leftIntro\"></div>\\n          </div>\\n        </div>\\n        <div class=\"title\">\\n          <div class=\"top\">INTRODUCTION</div>\\n          <div class=\"middle\">二中介绍</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n      </div> -->\\n      <div>\\n        <div>\\n          <div>EVENTS</div>\\n          <div>二中要闻</div>\\n          </div>\\n        </div>\\n      <div>\\n        <div>\\n          <div>\\n            <div>TEACHERS</div>\\n            <div>二中名师</div>\\n          </div>\\n          <div>优秀教师大批涌现，教师发展硕果累累</div>\\n        </div>\\n        <div>\\n          <div>TEACHERS</div>\\n          <div>二中名师</div>\\n          </div>  \\n        </div>\\n      <div>\\n        <div>\\n          <div>\\n            <div>STUDENTS</div>\\n            <div>二中学子</div>\\n          </div>\\n          <div>“全人发展”课程，搭建学生展翅高飞的天空</div>\\n        </div>\\n        <div>\\n          <div>STUDENTS</div>\\n          <div>二中学子</div>\\n          </div> \\n        </div>\\n      <div>\\n        <div>\\n          <div>\\n            <div>VIDEO</div>\\n            <div>宣传视频</div>\\n          </div>\\n          <div>青岛二中是一所有近百年历史的老牌名校，历年的入学录取成绩、高考成绩、竞赛成绩都保持青岛市第一</div>\\n        </div>\\n        <!-- 手机 -->\\n        <div>\\n          <div>VIDEO</div>\\n          <div>宣传视频</div>\\n          </div>\\n        </div>\\n      \\n      <!-- <div class=\"home_fifth\">\\n        <div class=\"title\">\\n          <div class=\"top\">EVENTS</div>\\n          <div class=\"middle\">快速通道</div>\\n          <div class=\"bottom\"></div>\\n        </div>\\n        <div class=\"passage\">\\n          <div class=\"line\">\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/educate.png\">\\n              <span>教育悟语</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/famousteacher.png\">\\n              <span>名师讲堂</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/homeschool.png\">\\n              <span>家长学校</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/speech.png\">\\n              <span>国旗下演讲</span>\\n            </div>\\n          </div>\\n          <div class=\"line\">\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/magazine.png\">\\n              <span>校报校刊</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/schoolaffairs.png\">\\n              <span>校务公开</span>\\n            </div>\\n            <div>\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/channel.png\">\\n              <span>内网通道</span>\\n            </div>\\n            <div onclick=\"showMailBox()\">\\n              <img src=\"http://img.qderzhong.net/thumb/erzhong/mailbox.png\">\\n              <span>校长信箱</span>\\n            </div>\\n          </div>\\n        </div>\\n      </div> -->\\n      <!-- 底部footer -->\\n      <div>\\n        <div>\\n          <div>\\n            <img src=\"./static/images/qrcode.png\">\\n            <a>校长邮箱入口</a>\\n          </div>\\n          \\n        </div>\\n        <div>\\n          <div>\\n            <a href=\"#\">友情链接：</a>\\n            <a href=\"http://edu.qingdao.gov.cn/\">青岛市教育局</a>\\n          </div>\\n        </div>\\n        <div>\\n          <div>\\n            <a href=\"#\">版权所有©山东省青岛第二中学 <img src=\"./static/images/banquan.png\"></a>\\n            <a href=\"https://beian.miit.gov.cn/#/Integrated/index\">备案序号：鲁ICP备10005652号-2</a>\\n            <a href=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=37021202000507\"><img src=\"./static/images/record.png\">鲁公网安备：37021202000507号</a>\\n            <a>校长邮箱入口</a>\\n          </div> \\n        </div>      \\n        <div>\\n          <div>\\n            <a href=\"http://edu.qingdao.gov.cn/\">青岛市教育局</a>\\n            <a href=\"https://beian.miit.gov.cn/#/Integrated/index\">鲁ICP备10005652号-2</a>\\n            <a href=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=37021202000507\"><img src=\"./static/images/record.png\">鲁公网安备：37021202000507号</a>\\n            <a href=\"#\"><img src=\"./static/images/banquan.png\">版权所有©山东省青岛第二中学</a>\\n          </div>          \\n        </div>\\n      </div>\\n    </div>\\n  </div>\\n  </body>\\n</html>', media={'images': [], 'videos': [], 'audios': []}, links={'internal': [{'href': 'http://www.qderzhong.net/index.html', 'text': '', 'title': '', 'base_domain': 'qderzhong.net', 'head_data': None, 'head_extraction_status': None, 'head_extraction_error': None, 'intrinsic_score': 0.0, 'contextual_score': None, 'total_score': None}, {'href': 'http://www.qderzhong.net/erjidetail.html', 'text': 'English', 'title': '', 'base_domain': 'qderzhong.net', 'head_data': None, 'head_extraction_status': None, 'head_extraction_error': None, 'intrinsic_score': 0.0, 'contextual_score': None, 'total_score': None}, {'href': 'http://www.qderzhong.net/', 'text': '友情链接：', 'title': '', 'base_domain': 'qderzhong.net', 'head_data': None, 'head_extraction_status': None, 'head_extraction_error': None, 'intrinsic_score': 0.0, 'contextual_score': None, 'total_score': None}], 'external': [{'href': 'http://edu.qingdao.gov.cn/', 'text': '青岛市教育局', 'title': '', 'base_domain': 'qingdao.gov.cn', 'head_data': None, 'head_extraction_status': None, 'head_extraction_error': None, 'intrinsic_score': 0.0, 'contextual_score': None, 'total_score': None}, {'href': 'https://beian.miit.gov.cn/', 'text': '备案序号：鲁ICP备10005652号-2', 'title': '', 'base_domain': 'miit.gov.cn', 'head_data': None, 'head_extraction_status': None, 'head_extraction_error': None, 'intrinsic_score': 0.0, 'contextual_score': None, 'total_score': None}, {'href': 'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=37021202000507', 'text': '鲁公网安备：37021202000507号', 'title': '', 'base_domain': 'beian.gov.cn', 'head_data': None, 'head_extraction_status': None, 'head_extraction_error': None, 'intrinsic_score': 0.0, 'contextual_score': None, 'total_score': None}]}, downloaded_files=None, js_execution_result=None, screenshot=None, pdf=None, mhtml=None, extracted_content=None, metadata={'title': '青岛二中', 'description': '青岛二中', 'keywords': '青岛二中', 'author': None}, error_message='', session_id=None, response_headers={'connection': 'keep-alive', 'content-encoding': 'gzip', 'content-type': 'text/html', 'date': 'Sun, 10 Aug 2025 12:28:18 GMT', 'etag': 'W/\"6874c258-950f\"', 'keep-alive': 'timeout=4', 'last-modified': 'Mon, 14 Jul 2025 08:39:52 GMT', 'proxy-connection': 'keep-alive', 'server': 'nginx', 'transfer-encoding': 'chunked', 'vary': 'Accept-Encoding'}, status_code=200, ssl_certificate=None, dispatch_result=None, redirected_url='http://www.qderzhong.net/', network_requests=None, console_messages=None, tables=[])]"}, "methods": null}