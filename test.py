from crawl4ai import AsyncWebCrawler, CrawlerRunConfig

async def main():
    run_config = CrawlerRunConfig(
        page_timeout=120000,  # 加载页面超时120秒，单位毫秒
        wait_for="css:#some-element",  # 等待某个元素出现代替默认事件
        simulate_user=True,  # 模拟用户行为，提升反爬能力
        verbose=True,        # 打印详细日志方便调试
        # 其他配置参数...
    )

    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url="http://172.30.100.2/",
            config=run_config
        )
        print(result.markdown)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
