import asyncio
import argparse
from crawl4ai import AsyncWebCrawler
from datetime import datetime
import json
import aiohttp
import re
import random

from rich import print as rprint

async def get_proxy_list():
    """从代理池API获取代理列表"""
    proxy_api_url = "http://api.89ip.cn/tqdl.html?api=1&num=60&port=&address=&isp="
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(proxy_api_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    text = await response.text()
                    # 解析代理IP，格式通常是 IP:PORT
                    proxy_pattern = r'(\d+\.\d+\.\d+\.\d+:\d+)'
                    proxies = re.findall(proxy_pattern, text)
                    
                    if proxies:
                        rprint(f"[green]✅ 成功获取到 {len(proxies)} 个代理[/green]")
                        return proxies
                    else:
                        rprint("[yellow]⚠️ 未找到有效代理，将使用直连[/yellow]")
                        return []
                else:
                    rprint(f"[red]❌ 代理API请求失败，状态码: {response.status}[/red]")
                    return []
    except Exception as e:
        rprint(f"[red]❌ 获取代理列表失败: {e}[/red]")
        return []

async def try_single_crawl(target_url, strategy_name, crawler_config):
    """尝试单次爬取"""
    try:
        async with AsyncWebCrawler(**crawler_config) as crawler:
            rprint(f"[yellow]正在尝试: {strategy_name}[/yellow]")
            
            # 使用较短的超时时间
            result = await asyncio.wait_for(
                crawler.arun(url=target_url),
                timeout=15.0  # 15秒超时
            )
            
            if result and hasattr(result, 'html') and result.html:
                rprint(f"[green]✅ 成功！使用: {strategy_name}[/green]")
                return result, strategy_name
            else:
                rprint(f"[yellow]⚠️ {strategy_name} 返回空结果[/yellow]")
                return None, None
                
    except asyncio.TimeoutError:
        rprint(f"[red]❌ {strategy_name} 超时[/red]")
        return None, None
    except Exception as e:
        rprint(f"[red]❌ {strategy_name} 失败: {str(e)[:100]}...[/red]")
        return None, None

async def crawl_with_strategies(target_url):
    """使用多种策略进行爬取"""
    
    # 策略1: 直连
    strategies = [
        {
            "name": "直连",
            "config": {"headless": True, "verbose": False}
        }
    ]
    
    # 策略2: 获取代理并添加到策略列表
    rprint("[cyan]正在获取代理列表...[/cyan]")
    proxy_list = await get_proxy_list()
    
    if proxy_list:
        # 随机选择3个代理
        selected_proxies = random.sample(proxy_list, min(3, len(proxy_list)))
        for proxy in selected_proxies:
            strategies.append({
                "name": f"代理 {proxy}",
                "config": {
                    "headless": True, 
                    "verbose": False,
                    "proxy": f"http://{proxy}"
                }
            })
    
    rprint(f"[cyan]将尝试 {len(strategies)} 种策略[/cyan]")
    
    # 依次尝试每种策略
    for i, strategy in enumerate(strategies, 1):
        rprint(f"[blue]策略 {i}/{len(strategies)}[/blue]")
        
        result, used_strategy = await try_single_crawl(
            target_url, 
            strategy["name"], 
            strategy["config"]
        )
        
        if result:
            return result, used_strategy
        
        # 等待一下再尝试下一个策略
        if i < len(strategies):
            await asyncio.sleep(2)
    
    return None, None

async def main():
    rprint("[bold yellow]DIAGNOSTIC MODE SCRIPT WITH PROXY POOL[/bold yellow]")
    rprint("[italic]职责：使用代理池策略爬取网页并输出JSON诊断结果。[/italic]\n")

    # --- 参数解析 ---
    parser = argparse.ArgumentParser(description="带代理池的诊断脚本", formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("--url", required=True, help="目标网页URL")
    args = parser.parse_args()

    # --- 核心诊断逻辑 ---
    rprint(f"正在尝试爬取目标URL: {args.url}")
    
    # 使用策略进行爬取
    result, used_strategy = await crawl_with_strategies(args.url)
    
    if result:
        try:
            # 【核心诊断代码】
            rprint("\n\n[bold magenta]============== DIAGNOSTIC MODE: RESULT INSPECTION ===============[/bold magenta]")
            rprint("成功获取到 'result' 对象。正在检查其内部结构...")
            rprint(f"[green]使用的连接策略: {used_strategy}[/green]")
            
            # 打印对象类型
            object_type = str(type(result))
            rprint(f"\n[cyan]对象类型 (Object Type):[/cyan] {object_type}")
            
            # 准备要保存到JSON的数据
            diagnostic_data = {
                "timestamp": datetime.now().isoformat(),
                "url": args.url,
                "used_strategy": used_strategy,
                "object_type": object_type,
                "attributes": None,
                "methods": None
            }
            
            # 使用 vars() 打印所有属性及其值
            rprint("\n[cyan]对象的属性和值 (Attributes and Values via vars()):[/cyan]")
            try:
                result_vars = vars(result)
                rprint(result_vars)
                
                # 将属性转换为可序列化的格式
                serializable_vars = {}
                for key, value in result_vars.items():
                    try:
                        json.dumps(value)
                        serializable_vars[key] = value
                    except (TypeError, ValueError):
                        serializable_vars[key] = str(value)
                
                diagnostic_data["attributes"] = serializable_vars
                
            except TypeError:
                rprint("[yellow]vars() failed. Falling back to dir()...[/yellow]")
                methods_and_attrs = dir(result)
                rprint(methods_and_attrs)
                diagnostic_data["methods"] = methods_and_attrs

            # 生成JSON文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_filename = f"diagnostic_result_{timestamp}.json"
            
            try:
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(diagnostic_data, f, ensure_ascii=False, indent=2)
                rprint(f"\n[bold green]✅ 诊断数据已保存到: {json_filename}[/bold green]")
            except Exception as e:
                rprint(f"\n[bold red]❌ 保存JSON文件时出错: {e}[/bold red]")

            rprint("\n[bold magenta]===================================================================[/bold magenta]\n")
            rprint("[bold green]诊断完成！[/bold green]")
            
        except Exception as e:
            rprint(f"\n[bold red]处理诊断结果时发生错误: {e}[/bold red]")
    else:
        rprint(f"\n[bold red]所有策略都失败了，无法爬取目标URL: {args.url}[/bold red]")
        
        # 生成失败报告JSON
        error_data = {
            "timestamp": datetime.now().isoformat(),
            "url": args.url,
            "error_type": "all_strategies_failed",
            "message": "所有策略都失败了"
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        error_filename = f"error_report_{timestamp}.json"
        
        try:
            with open(error_filename, 'w', encoding='utf-8') as f:
                json.dump(error_data, f, ensure_ascii=False, indent=2)
            rprint(f"[yellow]错误报告已保存到: {error_filename}[/yellow]")
        except Exception as save_error:
            rprint(f"[red]无法保存错误报告: {save_error}[/red]")

if __name__ == "__main__":
    asyncio.run(main())
