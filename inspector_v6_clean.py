import asyncio
import argparse
from crawl4ai import AsyncWebCrawler
from datetime import datetime
import json

from rich import print as rprint

async def main():
    rprint("[bold yellow]DIAGNOSTIC MODE SCRIPT[/bold yellow]")
    rprint("[italic]职责：捕获一个爬取结果对象，并打印其内部所有可用的属性。[/italic]\n")

    # --- 参数解析 ---
    parser = argparse.ArgumentParser(description="诊断版脚本，用于检查CrawlResult对象的内部结构。", formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("--url", required=True, help="用于诊断的单个目标网页URL。")
    args = parser.parse_args()

    # --- 核心诊断逻辑 ---
    async with AsyncWebCrawler(headless=True, verbose=True) as crawler:
        rprint(f"正在尝试爬取诊断目标URL: {args.url}")
        try:
            # 执行爬取
            result = await crawler.arun(url=args.url)

            # 【核心诊断代码】
            # 成功获取到 result 对象后，打印其内部结构然后退出
            rprint("\n\n[bold magenta]============== DIAGNOSTIC MODE: RESULT INSPECTION ===============[/bold magenta]")
            rprint("成功获取到 'result' 对象。正在检查其内部结构...")
            
            # 打印对象类型
            object_type = str(type(result))
            rprint(f"\n[cyan]对象类型 (Object Type):[/cyan] {object_type}")
            
            # 准备要保存到JSON的数据
            diagnostic_data = {
                "timestamp": datetime.now().isoformat(),
                "url": args.url,
                "object_type": object_type,
                "attributes": None,
                "methods": None
            }
            
            # 使用 vars() 打印所有属性及其值，这是最清晰的方式
            rprint("\n[cyan]对象的属性和值 (Attributes and Values via vars()):[/cyan]")
            try:
                # vars() 会以字典形式展示对象的属性，非常直观
                result_vars = vars(result)
                rprint(result_vars)
                
                # 将属性转换为可序列化的格式
                serializable_vars = {}
                for key, value in result_vars.items():
                    try:
                        # 尝试JSON序列化测试
                        json.dumps(value)
                        serializable_vars[key] = value
                    except (TypeError, ValueError):
                        # 如果不能序列化，转换为字符串
                        serializable_vars[key] = str(value)
                
                diagnostic_data["attributes"] = serializable_vars
                
            except TypeError:
                # 如果 vars() 不适用，我们回退到使用 dir()
                rprint("[yellow]vars() failed. Falling back to dir() to list all attributes and methods...[/yellow]")
                methods_and_attrs = dir(result)
                rprint(methods_and_attrs)
                diagnostic_data["methods"] = methods_and_attrs

            # 生成JSON文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_filename = f"diagnostic_result_{timestamp}.json"
            
            try:
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(diagnostic_data, f, ensure_ascii=False, indent=2)
                rprint(f"\n[bold green]✅ 诊断数据已保存到: {json_filename}[/bold green]")
            except Exception as e:
                rprint(f"\n[bold red]❌ 保存JSON文件时出错: {e}[/bold red]")

            rprint("\n[bold magenta]===================================================================[/bold magenta]\n")

            rprint("[bold green]诊断完成。请复制以上'DIAGNOSTIC MODE'块内的所有内容并回复。程序已终止。[/bold green]")

        except Exception as e:
            rprint(f"\n[bold red]爬取过程中发生错误，无法进行诊断: {e}[/bold red]")
            
            # 生成错误报告JSON
            error_data = {
                "timestamp": datetime.now().isoformat(),
                "url": args.url,
                "error_type": "crawling_failed",
                "error_message": str(e)
            }
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_filename = f"error_report_{timestamp}.json"
            
            try:
                with open(error_filename, 'w', encoding='utf-8') as f:
                    json.dump(error_data, f, ensure_ascii=False, indent=2)
                rprint(f"[yellow]错误报告已保存到: {error_filename}[/yellow]")
            except Exception as save_error:
                rprint(f"[red]无法保存错误报告: {save_error}[/red]")

if __name__ == "__main__":
    asyncio.run(main())
