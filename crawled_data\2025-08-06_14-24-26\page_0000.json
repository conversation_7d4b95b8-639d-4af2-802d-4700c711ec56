{"url": "http://www.qd03.qdedu.net/", "crawl_timestamp": "2025-08-06T14:25:27.798309", "status": "failed", "page_title": "Title Not Found", "visible_text": "", "raw_html": "", "screenshot_path": null, "internal_error_message": "Unexpected error in _crawl_web at line 763 in _crawl_web (..\\..\\Python\\Lib\\site-packages\\crawl4ai\\async_crawler_strategy.py):\nError: Failed on navigating ACS-GOTO:\nPage.goto: Timeout 60000ms exceeded.\nCall log:\n  - navigating to \"http://www.qd03.qdedu.net/\", waiting until \"domcontentloaded\"\n\n\nCode context:\n 758                               tag=\"GOTO\",\n 759                               params={\"url\": url},\n 760                           )\n 761                           response = None\n 762                       else:\n 763 →                         raise RuntimeError(f\"Failed on navigating ACS-GOTO:\\n{str(e)}\")\n 764   \n 765                   await self.execute_hook(\n 766                       \"after_goto\", page, context=context, url=url, response=response, config=config\n 767                   )\n 768   "}