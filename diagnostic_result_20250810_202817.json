{"timestamp": "2025-08-10T20:28:17.956516", "url": "http://www.qd03.qdedu.net", "object_type": "<class 'crawl4ai.models.CrawlResultContainer'>", "attributes": {"_results": "[CrawlResult(url='http://www.qd03.qdedu.net', html='', fit_html=<property object at 0x0000024860F0D080>, success=False, cleaned_html=None, media={}, links={}, downloaded_files=None, js_execution_result=None, screenshot=None, pdf=None, mhtml=None, extracted_content=None, metadata=None, error_message='Unexpected error in _crawl_web at line 763 in _crawl_web (..\\\\..\\\\Python\\\\Lib\\\\site-packages\\\\crawl4ai\\\\async_crawler_strategy.py):\\nError: Failed on navigating ACS-GOTO:\\nPage.goto: Timeout 60000ms exceeded.\\nCall log:\\n  - navigating to \"http://www.qd03.qdedu.net/\", waiting until \"domcontentloaded\"\\n\\n\\nCode context:\\n 758                               tag=\"GOTO\",\\n 759                               params={\"url\": url},\\n 760                           )\\n 761                           response = None\\n 762                       else:\\n 763 →                         raise RuntimeError(f\"Failed on navigating ACS-GOTO:\\\\n{str(e)}\")\\n 764   \\n 765                   await self.execute_hook(\\n 766                       \"after_goto\", page, context=context, url=url, response=response, config=config\\n 767                   )\\n 768   ', session_id=None, response_headers=None, status_code=None, ssl_certificate=None, dispatch_result=None, redirected_url=None, network_requests=None, console_messages=None, tables=[])]"}, "methods": null}